#!/usr/bin/env node

const { execSync, spawn } = require('child_process');
const readline = require('readline');
const path = require('path');
const fs = require('fs');

// 配置 - 将在运行时由用户输入
let SOURCE_URL = '';

// 存储merge相关信息
let mergeInfo = {
    sourceUrl: '',
    sourceBranch: '',
    targetBranch: '',
    revisionChanges: [],
    commitLogs: []
};

// 创建readline接口
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// 颜色输出
const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
};

function log(message, color = 'reset') {
    console.log(colors[color] + message + colors.reset);
}

function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

function execCommand(command, options = {}) {
    try {
        // 在Windows系统上，SVN输出通常使用GBK编码
        // 我们需要使用buffer模式获取原始数据，然后手动转换编码
        const encoding = process.platform === 'win32' ? 'buffer' : 'utf8';

        const result = execSync(command, {
            encoding: encoding,
            stdio: options.silent ? 'pipe' : 'inherit',
            ...options
        });

        let output = result;

        // 如果是Windows系统且返回的是buffer，需要转换编码
        if (process.platform === 'win32' && Buffer.isBuffer(result)) {
            // 尝试使用iconv-lite转换GBK到UTF-8
            try {
                const iconv = require('iconv-lite');
                output = iconv.decode(result, 'gbk');
            } catch (iconvError) {
                // 如果iconv-lite不可用，回退到默认的toString()
                log('警告: 无法使用iconv-lite转换编码，可能出现乱码。建议安装: npm install iconv-lite', 'yellow');
                output = result.toString('utf8');
            }
        }

        return { success: true, output: output };
    } catch (error) {
        let errorOutput = error.stdout || error.stderr || '';

        // 同样处理错误输出的编码
        if (process.platform === 'win32' && Buffer.isBuffer(errorOutput)) {
            try {
                const iconv = require('iconv-lite');
                errorOutput = iconv.decode(errorOutput, 'gbk');
            } catch (iconvError) {
                errorOutput = errorOutput.toString('utf8');
            }
        }

        return {
            success: false,
            error: error.message,
            output: errorOutput
        };
    }
}

async function checkSvnAvailable() {
    log('检查SVN是否可用...', 'blue');
    const result = execCommand('svn --version', { silent: true });

    if (!result.success) {
        log('错误: 未找到SVN命令！', 'red');
        log('请确保SVN已安装并添加到系统PATH中。', 'yellow');
        return false;
    }

    log('SVN命令可用。', 'green');
    return true;
}

async function checkSvnWorkingCopy() {
    log('检查当前目录是否为SVN工作副本...', 'blue');
    const result = execCommand('svn info .', { silent: true });

    if (!result.success) {
        log('错误: 当前目录不是SVN工作副本！', 'red');
        log('请确保在SVN工作副本目录中运行此脚本。', 'yellow');
        log(`当前目录: ${process.cwd()}`, 'cyan');
        return false;
    }

    log('当前目录是有效的SVN工作副本。', 'green');
    return true;
}

async function showCurrentStatus() {
    log('\n当前SVN状态:', 'cyan');
    log('='.repeat(40), 'cyan');
    execCommand('svn status');
    console.log();
}

async function updateWorkingCopy() {
    log('更新SVN工作副本...', 'blue');
    log('='.repeat(40), 'blue');
    log('正在执行: svn update', 'cyan');

    const result = execCommand('svn update');

    if (!result.success) {
        log('\n错误: SVN update 执行失败！', 'red');
        log('错误详情:', 'red');
        log(result.output, 'red');
        return false;
    }

    log('\nSVN工作副本更新完成！', 'green');
    return true;
}

async function checkMergeAvailability() {
    log('\n检查可用的merge信息...', 'blue');
    log('='.repeat(40), 'blue');

    // 检查merge信息
    log('正在执行: svn mergeinfo --show-revs eligible .', 'cyan');
    const mergeInfoResult = execCommand(`svn mergeinfo --show-revs eligible "${SOURCE_URL}" .`, { silent: true });

    if (mergeInfoResult.success && mergeInfoResult.output.trim()) {
        log('发现可合并的版本:', 'green');
        const revisions = mergeInfoResult.output.trim().split('\n');
        revisions.slice(0, 10).forEach(rev => log(`  ${rev}`, 'cyan')); // 只显示前10个
        if (revisions.length > 10) {
            log(`  ... 还有 ${revisions.length - 10} 个版本`, 'yellow');
        }
        return true;
    } else {
        log('没有发现可合并的版本，或者已经是最新的。', 'yellow');
        return false;
    }
}

async function performTestMerge() {
    log('开始执行SVN test merge...', 'blue');
    log('='.repeat(40), 'blue');

    // 首先检查源仓库的最新版本
    log('检查源仓库信息...', 'cyan');
    const sourceInfoResult = execCommand(`svn info "${SOURCE_URL}"`, { silent: true });

    if (sourceInfoResult.success) {
        const sourceRevision = sourceInfoResult.output.match(/Revision: (\d+)/);
        if (sourceRevision) {
            log(`源仓库最新版本: ${sourceRevision[1]}`, 'cyan');
        }
    }

    // 检查当前工作副本信息
    log('检查当前工作副本信息...', 'cyan');
    const localInfoResult = execCommand('svn info .', { silent: true });

    if (localInfoResult.success) {
        const localRevision = localInfoResult.output.match(/Revision: (\d+)/);
        if (localRevision) {
            log(`当前工作副本版本: ${localRevision[1]}`, 'cyan');
        }
    }

    console.log();
    log(`正在执行: svn merge --dry-run "${SOURCE_URL}" .`, 'cyan');
    log('这是一个测试运行，不会实际修改文件...', 'yellow');
    console.log();

    const result = execCommand(`svn merge --dry-run "${SOURCE_URL}" .`, { silent: true });

    if (!result.success) {
        log('错误: SVN test merge 执行失败！', 'red');
        log('错误详情:', 'red');
        log(result.output, 'red');
        log('请检查网络连接、仓库权限或URL是否正确。', 'yellow');
        return { success: false, hasChanges: false };
    }

    // 分析merge输出
    const output = result.output.trim();
    const hasChanges = output && output.length > 0;

    if (!hasChanges) {
        log('Test merge结果: 没有需要合并的更改', 'green');
        log('源仓库和当前工作副本已经是同步的。', 'green');
    } else {
        log('Test merge结果:', 'green');
        log('-'.repeat(40), 'cyan');
        console.log(output);
        log('-'.repeat(40), 'cyan');
    }

    log('\n='.repeat(40), 'green');
    log('Test merge 完成！', 'green');
    log('='.repeat(40), 'green');

    return { success: true, hasChanges };
}

async function performRealMerge() {
    log('\n开始执行真正的SVN merge...', 'blue');
    log('='.repeat(40), 'blue');
    log(`正在执行: svn merge "${SOURCE_URL}" .`, 'cyan');

    const result = execCommand(`svn merge "${SOURCE_URL}" .`);

    if (!result.success) {
        log('\n错误: SVN merge 执行失败！', 'red');
        log('错误详情:', 'red');
        log(result.output, 'red');
        log('请检查网络连接和仓库权限。', 'yellow');
        return false;
    }

    log('\n='.repeat(40), 'green');
    log('真正的SVN merge 完成！', 'green');
    log('='.repeat(40), 'green');

    return true;
}

async function checkConflicts() {
    log('\n真正merge后的状态:', 'cyan');
    const statusResult = execCommand('svn status', { silent: true });

    if (statusResult.success) {
        console.log(statusResult.output);

        // 检查冲突
        const conflicts = statusResult.output.split('\n').filter(line => line.startsWith('C '));

        if (conflicts.length > 0) {
            log('发现冲突文件，请手动解决冲突后再提交。', 'red');
            log('冲突文件列表:', 'yellow');
            conflicts.forEach(conflict => log(conflict, 'red'));
            log('\n解决冲突后，使用以下命令标记为已解决:', 'yellow');
            log('svn resolve --accept working [文件路径]', 'cyan');
            return false;
        } else {
            log('没有发现冲突文件。', 'green');
            return true;
        }
    }

    return true;
}

// 全局变量存储merge前的状态
let preMergeStatus = [];

async function capturePreMergeStatus() {
    log('记录merge前的文件状态...', 'blue');

    const statusResult = execCommand('svn status', { silent: true });

    if (statusResult.success) {
        preMergeStatus = statusResult.output.split('\n')
            .filter(line => line.trim())
            .map(line => line.trim());
        log(`已记录 ${preMergeStatus.length} 个文件的merge前状态。`, 'cyan');
    } else {
        log('无法记录merge前状态，将提交所有变更。', 'yellow');
        preMergeStatus = [];
    }
}

async function autoCommitMergedFiles() {
    log('\n检查merge后的文件状态...', 'blue');

    // 获取当前SVN状态
    const statusResult = execCommand('svn status', { silent: true });

    if (!statusResult.success) {
        log('无法获取SVN状态，跳过自动提交。', 'red');
        return false;
    }

    const currentStatusLines = statusResult.output.split('\n')
        .filter(line => line.trim())
        .map(line => line.trim());

    // 检查是否有冲突文件
    const conflictFiles = currentStatusLines.filter(line => line.startsWith('C '));

    if (conflictFiles.length > 0) {
        log('发现冲突文件，无法自动提交：', 'red');
        conflictFiles.forEach(conflict => log(`  ${conflict}`, 'red'));
        log('请手动解决冲突后再提交。', 'yellow');
        return false;
    }

    // 获取所有当前变更的文件
    const allChangedFiles = currentStatusLines.filter(line => {
        const status = line.charAt(0);
        return ['M', 'A', 'D'].includes(status);
    });

    // 过滤出只由merge产生的文件（排除merge前已存在的变更）
    const mergeOnlyFiles = allChangedFiles.filter(currentFile => {
        // 如果merge前没有记录状态，则提交所有文件
        if (preMergeStatus.length === 0) {
            return true;
        }

        // 检查这个文件在merge前是否已经有变更
        const wasChangedBefore = preMergeStatus.some(preFile => {
            // 比较文件路径（去掉状态字符）
            const currentPath = currentFile.substring(1).trim();
            const prePath = preFile.substring(1).trim();
            return currentPath === prePath;
        });

        // 只包含merge前没有变更的文件
        return !wasChangedBefore;
    });

    if (mergeOnlyFiles.length === 0) {
        if (allChangedFiles.length > 0) {
            log('所有变更的文件都是merge前已存在的本地变更，不进行自动提交。', 'yellow');
            log('merge前已存在的变更文件：', 'cyan');
            allChangedFiles.forEach(file => log(`  ${file}`, 'cyan'));
        } else {
            log('没有需要提交的文件。', 'green');
        }
        return true;
    }

    log(`发现 ${mergeOnlyFiles.length} 个由merge产生的新变更文件：`, 'green');

    log('发现以下需要提交的文件：', 'green');
    log('='.repeat(50), 'cyan');
    mergeOnlyFiles.forEach((file, index) => {
        const status = file.charAt(0);
        const fileName = file.substring(1).trim();
        let statusDesc = '';

        switch(status) {
        case 'M': statusDesc = '修改'; break;
        case 'A': statusDesc = '添加'; break;
        case 'D': statusDesc = '删除'; break;
        default: statusDesc = '其他';
        }

        log(`  ${index + 1}. [${statusDesc}] ${fileName}`, 'cyan');
    });
    log('='.repeat(50), 'cyan');

    // 如果有merge前的变更，也显示出来（但不提交）
    if (preMergeStatus.length > 0 && allChangedFiles.length > mergeOnlyFiles.length) {
        const preExistingChanges = allChangedFiles.filter(currentFile => {
            const currentPath = currentFile.substring(1).trim();
            return preMergeStatus.some(preFile => {
                const prePath = preFile.substring(1).trim();
                return currentPath === prePath;
            });
        });

        if (preExistingChanges.length > 0) {
            log('\n以下文件是merge前已存在的变更（不会被自动提交）：', 'yellow');
            log('-'.repeat(50), 'yellow');
            preExistingChanges.forEach((file, index) => {
                const status = file.charAt(0);
                const fileName = file.substring(1).trim();
                let statusDesc = '';

                switch(status) {
                case 'M': statusDesc = '修改'; break;
                case 'A': statusDesc = '添加'; break;
                case 'D': statusDesc = '删除'; break;
                default: statusDesc = '其他';
                }

                log(`  ${index + 1}. [${statusDesc}] ${fileName}`, 'yellow');
            });
            log('-'.repeat(50), 'yellow');
        }
    }

    // 构建提交信息
    const commitMessage = generateCommitMessage(SOURCE_URL);

    // 显示完整的SVN命令
    log('\n将执行的SVN命令:', 'yellow');
    log(`svn commit -m "${commitMessage}"`, 'cyan');

    // 询问用户确认
    const confirmCommit = await question(`\n是否要提交以上 ${mergeOnlyFiles.length} 个merge产生的文件? (y/N): `);

    if (confirmCommit.toLowerCase() !== 'y') {
        log('用户取消了自动提交。', 'yellow');
        log('你可以稍后手动提交这些文件。', 'cyan');
        return false;
    }

    // 执行提交
    log('\n正在提交文件...', 'blue');
    const commitResult = execCommand(`svn commit -m "${commitMessage}"`);

    if (commitResult.success) {
        log('\n✅ 自动提交成功！', 'green');
        log(`已成功提交 ${mergeOnlyFiles.length} 个merge产生的文件。`, 'green');
        return true;
    } else {
        log('\n❌ 自动提交失败：', 'red');
        log(commitResult.output, 'red');
        log('请手动检查并提交。', 'yellow');
        return false;
    }
}

async function getCommitLogs() {
    log('获取提交日志...', 'blue');

    if (mergeInfo.revisionChanges.length === 0) {
        log('没有版本变更，跳过日志获取', 'yellow');
        return;
    }

    const logs = [];
    const maxLogs = 10; // 最多获取10个版本的日志
    const revisionsToProcess = mergeInfo.revisionChanges.slice(0, maxLogs);

    for (const revision of revisionsToProcess) {
        try {
            log(`获取版本 r${revision} 的日志...`, 'cyan');

            const logResult = execCommand(`svn log -r ${revision} "${SOURCE_URL}"`, { silent: true });

            if (logResult.success) {
                const logOutput = logResult.output.trim();
                const lines = logOutput.split('\n');

                // 解析SVN日志格式
                let author = '';
                let date = '';
                let message = '';

                // 第二行通常包含作者、日期等信息
                if (lines.length > 1) {
                    const infoLine = lines[1];
                    const infoMatch = infoLine.match(/r\d+ \| ([^|]+) \| ([^|]+) \|/);
                    if (infoMatch) {
                        author = infoMatch[1].trim();
                        date = infoMatch[2].trim().split(' ')[0]; // 只取日期部分
                    }
                }

                // 提交消息从第4行开始（跳过分隔线）
                if (lines.length > 3) {
                    const messageLines = [];
                    for (let i = 3; i < lines.length; i++) {
                        if (lines[i].startsWith('---')) {
                            break;
                        }
                        messageLines.push(lines[i]);
                    }
                    message = messageLines.join(' ').trim();

                    // 限制消息长度
                    if (message.length > 100) {
                        message = message.substring(0, 100) + '...';
                    }
                }

                logs.push({
                    revision: revision,
                    author: author,
                    date: date,
                    message: message
                });

            } else {
                log(`无法获取版本 r${revision} 的日志`, 'yellow');
            }
        } catch (error) {
            log(`获取版本 r${revision} 日志时出错: ${error.message}`, 'yellow');
        }
    }

    mergeInfo.commitLogs = logs;

    if (logs.length > 0) {
        log(`成功获取 ${logs.length} 个版本的提交日志`, 'green');
        logs.slice(0, 3).forEach(log => {
            console.log(`  r${log.revision} (${log.date}) ${log.author}: ${log.message}`);
        });
        if (logs.length > 3) {
            console.log(`  ... 还有 ${logs.length - 3} 个版本的日志`);
        }
    }
}

async function captureRevisionChanges() {
    log('检测版本号变更...', 'blue');

    // 获取可合并的版本列表
    const mergeInfoResult = execCommand(`svn mergeinfo --show-revs eligible "${SOURCE_URL}" .`, { silent: true });

    if (mergeInfoResult.success && mergeInfoResult.output.trim()) {
        const revisions = mergeInfoResult.output.trim().split('\n')
            .map(rev => rev.trim())
            .filter(rev => rev.startsWith('r'))
            .map(rev => rev.substring(1)); // 移除 'r' 前缀

        mergeInfo.revisionChanges = revisions;

        log(`发现 ${revisions.length} 个可合并的版本号:`, 'green');
        if (revisions.length > 0) {
            const firstRev = revisions[0];
            const lastRev = revisions[revisions.length - 1];
            log(`版本范围: r${firstRev} 到 r${lastRev}`, 'cyan');

            // 只显示前5个和后5个版本号
            const displayRevs = revisions.length > 10
                ? [...revisions.slice(0, 5), '...', ...revisions.slice(-5)]
                : revisions;

            displayRevs.forEach(rev => {
                if (rev === '...') {
                    log(`  ${rev}`, 'yellow');
                } else {
                    log(`  r${rev}`, 'cyan');
                }
            });
        }
    } else {
        log('没有发现可合并的版本', 'yellow');
        mergeInfo.revisionChanges = [];
    }
}

async function getCurrentBranchInfo() {
    log('获取当前分支信息...', 'blue');

    const infoResult = execCommand('svn info .', { silent: true });

    if (infoResult.success) {
        const urlMatch = infoResult.output.match(/URL: (.+)/);
        if (urlMatch) {
            const currentUrl = urlMatch[1].trim();

            if (currentUrl.includes('/trunk_vue')) {
                mergeInfo.targetBranch = 'trunk_vue';
            } else if (currentUrl.includes('/branches/')) {
                const branchMatch = currentUrl.match(/\/branches\/([^\/]+)/);
                mergeInfo.targetBranch = branchMatch ? branchMatch[1] : 'unknown_branch';
            } else if (currentUrl.includes('/trunk')) {
                mergeInfo.targetBranch = 'trunk';
            } else {
                mergeInfo.targetBranch = 'unknown';
            }
        }
    } else {
        mergeInfo.targetBranch = 'unknown';
        log('无法获取当前分支信息', 'yellow');
    }
}

async function getUserInput() {
    log('请输入merge from地址:', 'cyan');
    log('例如: https://cloudgroup.mindray.com/repos/2202/01-瑞影云++/03-Software/1-Source/09-Reviewer/01-code/webIM/trunk_vue', 'yellow');
    log('或者: https://cloudgroup.mindray.com/repos/2202/01-瑞影云++/03-Software/1-Source/09-Reviewer/01-code/webIM/branches/某个分支名', 'yellow');
    console.log();

    const sourceUrl = await question('请输入源仓库URL: ');

    if (!sourceUrl.trim()) {
        log('错误: 源仓库URL不能为空！', 'red');
        process.exit(1);
    }

    // 验证URL格式
    if (!sourceUrl.includes('cloudgroup.mindray.com/repos')) {
        log('警告: 输入的URL格式可能不正确', 'yellow');
        const confirm = await question('是否继续? (y/N): ');
        if (confirm.toLowerCase() !== 'y') {
            log('操作已取消。', 'yellow');
            process.exit(0);
        }
    }

    SOURCE_URL = sourceUrl.trim();

    // 记录merge信息
    mergeInfo.sourceUrl = SOURCE_URL;

    // 解析源分支信息
    if (SOURCE_URL.includes('/trunk_vue')) {
        mergeInfo.sourceBranch = 'trunk_vue';
    } else if (SOURCE_URL.includes('/branches/')) {
        const branchMatch = SOURCE_URL.match(/\/branches\/([^\/]+)/);
        mergeInfo.sourceBranch = branchMatch ? branchMatch[1] : 'unknown_branch';
    } else if (SOURCE_URL.includes('/trunk')) {
        mergeInfo.sourceBranch = 'trunk';
    } else {
        mergeInfo.sourceBranch = 'unknown';
    }

    // 获取当前分支信息
    await getCurrentBranchInfo();

    log(`已设置源仓库路径: ${SOURCE_URL}`, 'green');
    log(`源分支: ${mergeInfo.sourceBranch}`, 'cyan');
    log(`目标分支: ${mergeInfo.targetBranch}`, 'cyan');
    console.log();
}

function generateCommitMessage(sourceUrl) {
    // 基础commit信息
    let branchInfo = '';
    if (sourceUrl.includes('/trunk_vue')) {
        branchInfo = 'trunk_vue主干';
    } else if (sourceUrl.includes('/branches/')) {
        const branchMatch = sourceUrl.match(/\/branches\/([^\/]+)/);
        if (branchMatch) {
            branchInfo = `${branchMatch[1]}分支`;
        } else {
            branchInfo = '指定分支';
        }
    } else if (sourceUrl.includes('/trunk')) {
        branchInfo = 'trunk主干';
    } else {
        branchInfo = '指定源';
    }

    let baseMessage = `【其他】【更改描述】自动提交merged from ${mergeInfo.sourceUrl} 版本号变更 (共${mergeInfo.revisionChanges.length}个版本):${mergeInfo.revisionChanges.map(r => `r${r}`).join(', ')}【影响分析】合并${branchInfo}代码到当前分支，更新相关功能模块【测试建议】验证合并后的功能是否正常，检查是否有冲突或异常`;

    // 添加详细的merge信息
    let detailInfo = '\n\n=== Merge详细信息 ===';
    detailInfo += `\n源分支地址: ${mergeInfo.sourceUrl}`;
    detailInfo += `\n源分支: ${mergeInfo.sourceBranch}`;
    detailInfo += `\n目标分支: ${mergeInfo.targetBranch}`;

    // 添加版本号变更信息
    // if (mergeInfo.revisionChanges.length > 0) {
    //     detailInfo += `\n\n版本号变更 (共${mergeInfo.revisionChanges.length}个版本):`;

    //     if (mergeInfo.revisionChanges.length <= 5) {
    //         // 如果版本数量少，显示所有版本
    //         mergeInfo.revisionChanges.forEach(rev => {
    //             detailInfo += `\n  r${rev}`;
    //         });
    //     } else {
    //         // 如果版本数量多，显示范围
    //         const firstRev = mergeInfo.revisionChanges[0];
    //         const lastRev = mergeInfo.revisionChanges[mergeInfo.revisionChanges.length - 1];
    //         detailInfo += `\n  版本范围: r${firstRev} 到 r${lastRev}`;
    //         detailInfo += `\n  前3个版本: ${mergeInfo.revisionChanges.slice(0, 3).map(r => `r${r}`).join(', ')}`;
    //         detailInfo += `\n  后3个版本: ${mergeInfo.revisionChanges.slice(-3).map(r => `r${r}`).join(', ')}`;
    //     }
    // }

    // // 添加提交日志信息
    // if (mergeInfo.commitLogs.length > 0) {
    //     detailInfo += `\n\n提交日志 (显示前${Math.min(5, mergeInfo.commitLogs.length)}个):`;

    //     mergeInfo.commitLogs.slice(0, 5).forEach(log => {
    //         detailInfo += `\n  r${log.revision} (${log.date}) ${log.author}:`;
    //         detailInfo += `\n    ${log.message}`;
    //     });

    //     if (mergeInfo.commitLogs.length > 5) {
    //         detailInfo += `\n  ... 还有 ${mergeInfo.commitLogs.length - 5} 个版本的日志`;
    //     }
    // }

    return baseMessage
}

async function main() {
    try {
        log('========================================', 'magenta');
        log('SVN Merge 脚本 (Node.js版)', 'magenta');
        log('========================================', 'magenta');
        console.log();

        // 获取用户输入的源仓库地址
        await getUserInput();

        log(`当前目录: ${process.cwd()}`, 'cyan');
        console.log();

        // 检查SVN可用性
        if (!(await checkSvnAvailable())) {
            process.exit(1);
        }

        // 检查SVN工作副本
        if (!(await checkSvnWorkingCopy())) {
            process.exit(1);
        }

        // 显示当前状态
        await showCurrentStatus();

        // 询问是否继续
        const confirm = await question('是否继续执行SVN update和test merge操作? (y/N): ');
        if (confirm.toLowerCase() !== 'y') {
            log('操作已取消。', 'yellow');
            process.exit(0);
        }

        // 更新工作副本
        if (!(await updateWorkingCopy())) {
            process.exit(1);
        }

        // 记录merge前的文件状态
        await capturePreMergeStatus();

        // 检查merge可用性
        await checkMergeAvailability();

        // 捕获版本变更信息
        await captureRevisionChanges();

        // 获取提交日志
        await getCommitLogs();

        // 执行test merge
        const testResult = await performTestMerge();
        if (!testResult.success) {
            process.exit(1);
        }

        // 询问是否执行真正的merge
        let promptMessage;
        if (!testResult.hasChanges) {
            promptMessage = '\n没有检测到需要合并的更改，是否仍要执行真正的merge? (y/N): ';
        } else {
            promptMessage = '\n查看了test merge结果，是否执行真正的merge? (y/N): ';
        }

        const realMerge = await question(promptMessage);
        if (realMerge.toLowerCase() !== 'y') {
            if (!testResult.hasChanges) {
                log('操作已取消。源仓库和当前工作副本已经是同步的。', 'green');
            } else {
                log('真正的merge操作已取消。', 'yellow');
                log('如果需要，你可以稍后重新运行此脚本。', 'cyan');
            }
            process.exit(0);
        }

        // 执行真正的merge
        if (!(await performRealMerge())) {
            process.exit(1);
        }

        // 检查冲突并自动提交
        const noConflicts = await checkConflicts();

        // 如果没有冲突，自动提交
        if (noConflicts) {
            await autoCommitMergedFiles();
        }

        log('\n脚本执行完成。', 'green');

    } catch (error) {
        log(`\n发生错误: ${error.message}`, 'red');
        process.exit(1);
    } finally {
        rl.close();
    }
}

// 运行主函数
main();
